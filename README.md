# 天气应用

一个简单而美观的天气查询应用，使用HTML、CSS和JavaScript构建。

## 功能特性

- 🌤️ 实时天气查询
- 🌍 支持全球城市搜索
- 📱 响应式设计，支持移动设备
- 🎨 现代化UI设计
- ⚡ 快速加载和流畅动画
- 🔍 智能错误处理

## 显示信息

- 当前温度
- 体感温度
- 天气描述和图标
- 湿度
- 风速
- 气压
- 城市名称和国家

## 使用方法

### 1. 获取API密钥

1. 访问 [OpenWeatherMap](https://openweathermap.org/api)
2. 注册免费账户
3. 获取API密钥

### 2. 配置API密钥

在 `script.js` 文件中，将 `YOUR_API_KEY_HERE` 替换为您的实际API密钥：

```javascript
const API_KEY = '您的API密钥';
```

### 3. 运行应用

1. 直接在浏览器中打开 `index.html` 文件
2. 或者使用本地服务器（推荐）：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   
   # 使用PHP
   php -S localhost:8000
   ```

### 4. 使用应用

1. 在搜索框中输入城市名称（支持中文和英文）
2. 点击搜索按钮或按回车键
3. 查看天气信息

## 支持的城市格式

- 中文城市名：北京、上海、广州
- 英文城市名：London、New York、Tokyo
- 城市名+国家代码：Beijing,CN

## 技术栈

- **HTML5** - 页面结构
- **CSS3** - 样式和动画
- **JavaScript (ES6+)** - 交互逻辑
- **OpenWeatherMap API** - 天气数据
- **Font Awesome** - 图标库

## 文件结构

```
weather-app/
├── index.html          # 主页面
├── style.css           # 样式文件
├── script.js           # JavaScript逻辑
└── README.md           # 说明文档
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. 需要网络连接才能获取天气数据
2. API有请求限制（免费版每分钟60次）
3. 某些城市可能需要使用英文名称搜索

## 常见问题

**Q: 为什么搜索不到某个城市？**
A: 请尝试使用英文名称或检查拼写是否正确。

**Q: API密钥在哪里配置？**
A: 在 `script.js` 文件的第2行，替换 `YOUR_API_KEY_HERE`。

**Q: 如何获取免费的API密钥？**
A: 访问 OpenWeatherMap 官网注册账户即可获得免费API密钥。

## 许可证

MIT License - 可自由使用和修改。
