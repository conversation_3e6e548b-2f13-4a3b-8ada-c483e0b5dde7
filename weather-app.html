<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气应用 - 单文件版本</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .search-container {
            margin-bottom: 30px;
        }

        .search-box {
            display: flex;
            background: white;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .search-box:hover {
            transform: translateY(-2px);
        }

        #cityInput {
            flex: 1;
            padding: 15px 20px;
            border: none;
            outline: none;
            font-size: 1rem;
            background: transparent;
        }

        #cityInput::placeholder {
            color: #999;
        }

        #searchBtn {
            padding: 15px 20px;
            background: #0984e3;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s ease;
            min-width: 60px;
        }

        #searchBtn:hover {
            background: #0770c4;
        }

        .weather-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .loading {
            text-align: center;
            color: white;
            display: none;
        }

        .loading i {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .weather-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            display: none;
            animation: slideUp 0.5s ease;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .weather-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .weather-header h2 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 5px;
        }

        .weather-header p {
            color: #666;
            font-size: 0.9rem;
        }

        .weather-main {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .temperature {
            font-size: 3rem;
            font-weight: bold;
            color: #0984e3;
        }

        .unit {
            font-size: 1.5rem;
            color: #666;
        }

        .weather-icon img {
            width: 80px;
            height: 80px;
        }

        .weather-description {
            text-align: center;
            margin-bottom: 25px;
        }

        .weather-description p {
            font-size: 1.2rem;
            color: #555;
            text-transform: capitalize;
        }

        .weather-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .detail-item i {
            color: #0984e3;
            width: 16px;
        }

        .error-message {
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            display: none;
            animation: slideUp 0.5s ease;
        }

        .error-message i {
            font-size: 3rem;
            color: #e74c3c;
            margin-bottom: 15px;
        }

        .error-message h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .error-message p {
            color: #666;
            margin-bottom: 20px;
        }

        #retryBtn {
            background: #0984e3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        #retryBtn:hover {
            background: #0770c4;
        }

        footer {
            text-align: center;
            margin-top: 30px;
            color: white;
            opacity: 0.8;
            font-size: 0.9rem;
        }

        footer p {
            margin-bottom: 5px;
        }

        .api-config {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: white;
        }

        .api-config h3 {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .api-config input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .api-config button {
            background: #00b894;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .api-config button:hover {
            background: #00a085;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            header h1 {
                font-size: 2rem;
            }

            .weather-card {
                padding: 20px;
            }

            .temperature {
                font-size: 2.5rem;
            }

            .weather-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-cloud-sun"></i> 天气查询</h1>
            <p>输入城市名称查看当前天气</p>
        </header>

        <!-- API配置区域 -->
        <div class="api-config" id="apiConfig">
            <h3><i class="fas fa-key"></i> API配置</h3>
            <p style="font-size: 0.9rem; margin-bottom: 10px;">请输入您的OpenWeatherMap API密钥：</p>
            <input type="text" id="apiKeyInput" placeholder="在此输入您的API密钥" autocomplete="off">
            <button id="saveApiKey">保存密钥</button>
            <p style="font-size: 0.8rem; margin-top: 10px; opacity: 0.8;">
                没有API密钥？<a href="https://openweathermap.org/api" target="_blank" style="color: #74b9ff;">点击这里免费注册</a>
            </p>
        </div>

        <div class="search-container">
            <div class="search-box">
                <input type="text" id="cityInput" placeholder="请输入城市名称（如：北京、上海、London）" autocomplete="off">
                <button id="searchBtn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <div class="weather-container" id="weatherContainer">
            <div class="loading" id="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在获取天气信息...</p>
            </div>

            <div class="weather-card" id="weatherCard">
                <div class="weather-header">
                    <h2 id="cityName"></h2>
                    <p id="currentDate"></p>
                </div>

                <div class="weather-main">
                    <div class="temperature">
                        <span id="temperature"></span>
                        <span class="unit">°C</span>
                    </div>
                    <div class="weather-icon">
                        <img id="weatherIcon" src="" alt="天气图标">
                    </div>
                </div>

                <div class="weather-description">
                    <p id="weatherDescription"></p>
                </div>

                <div class="weather-details">
                    <div class="detail-item">
                        <i class="fas fa-eye"></i>
                        <span>体感温度</span>
                        <span id="feelsLike"></span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-tint"></i>
                        <span>湿度</span>
                        <span id="humidity"></span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-wind"></i>
                        <span>风速</span>
                        <span id="windSpeed"></span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-compress-arrows-alt"></i>
                        <span>气压</span>
                        <span id="pressure"></span>
                    </div>
                </div>
            </div>

            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>出错了</h3>
                <p id="errorText"></p>
                <button id="retryBtn">重试</button>
            </div>
        </div>

        <footer>
            <p>数据来源：OpenWeatherMap API</p>
            <p>单文件版本 - 便于分享和使用</p>
        </footer>
    </div>

    <script>
        // OpenWeatherMap API配置
        let API_KEY = localStorage.getItem('weatherApiKey') || '';
        const API_BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';

        // DOM元素
        const apiConfig = document.getElementById('apiConfig');
        const apiKeyInput = document.getElementById('apiKeyInput');
        const saveApiKeyBtn = document.getElementById('saveApiKey');
        const cityInput = document.getElementById('cityInput');
        const searchBtn = document.getElementById('searchBtn');
        const weatherContainer = document.getElementById('weatherContainer');
        const loading = document.getElementById('loading');
        const weatherCard = document.getElementById('weatherCard');
        const errorMessage = document.getElementById('errorMessage');
        const retryBtn = document.getElementById('retryBtn');

        // 天气信息元素
        const cityName = document.getElementById('cityName');
        const currentDate = document.getElementById('currentDate');
        const temperature = document.getElementById('temperature');
        const weatherIcon = document.getElementById('weatherIcon');
        const weatherDescription = document.getElementById('weatherDescription');
        const feelsLike = document.getElementById('feelsLike');
        const humidity = document.getElementById('humidity');
        const windSpeed = document.getElementById('windSpeed');
        const pressure = document.getElementById('pressure');
        const errorText = document.getElementById('errorText');

        // 事件监听器
        saveApiKeyBtn.addEventListener('click', saveApiKey);
        searchBtn.addEventListener('click', handleSearch);
        cityInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
        retryBtn.addEventListener('click', handleSearch);

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateCurrentDate();
            checkApiKey();
        });

        // 检查API密钥
        function checkApiKey() {
            if (API_KEY) {
                apiKeyInput.value = API_KEY.substring(0, 8) + '...';
                apiConfig.style.display = 'none';
            } else {
                apiConfig.style.display = 'block';
            }
        }

        // 保存API密钥
        function saveApiKey() {
            const key = apiKeyInput.value.trim();
            if (!key) {
                alert('请输入API密钥');
                return;
            }

            API_KEY = key;
            localStorage.setItem('weatherApiKey', key);
            apiConfig.style.display = 'none';
            alert('API密钥已保存！现在可以搜索天气了。');
        }

        // 处理搜索
        async function handleSearch() {
            const city = cityInput.value.trim();

            if (!city) {
                showError('请输入城市名称');
                return;
            }

            if (!API_KEY) {
                showError('请先配置您的OpenWeatherMap API密钥');
                apiConfig.style.display = 'block';
                return;
            }

            showLoading();

            try {
                const weatherData = await fetchWeatherData(city);
                displayWeatherData(weatherData);
            } catch (error) {
                console.error('获取天气数据失败:', error);
                showError(getErrorMessage(error));
            }
        }

        // 获取天气数据
        async function fetchWeatherData(city) {
            const url = `${API_BASE_URL}?q=${encodeURIComponent(city)}&appid=${API_KEY}&units=metric&lang=zh_cn`;

            const response = await fetch(url);

            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('CITY_NOT_FOUND');
                } else if (response.status === 401) {
                    throw new Error('INVALID_API_KEY');
                } else {
                    throw new Error('API_ERROR');
                }
            }

            return await response.json();
        }

        // 显示天气数据
        function displayWeatherData(data) {
            // 更新城市名称
            cityName.textContent = `${data.name}, ${data.sys.country}`;

            // 更新温度
            temperature.textContent = Math.round(data.main.temp);

            // 更新天气图标
            const iconCode = data.weather[0].icon;
            weatherIcon.src = `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
            weatherIcon.alt = data.weather[0].description;

            // 更新天气描述
            weatherDescription.textContent = data.weather[0].description;

            // 更新详细信息
            feelsLike.textContent = `${Math.round(data.main.feels_like)}°C`;
            humidity.textContent = `${data.main.humidity}%`;
            windSpeed.textContent = `${data.wind.speed} m/s`;
            pressure.textContent = `${data.main.pressure} hPa`;

            showWeatherCard();
        }

        // 显示状态管理
        function showLoading() {
            hideAllStates();
            loading.style.display = 'block';
        }

        function showWeatherCard() {
            hideAllStates();
            weatherCard.style.display = 'block';
        }

        function showError(message) {
            hideAllStates();
            errorText.textContent = message;
            errorMessage.style.display = 'block';
        }

        function hideAllStates() {
            loading.style.display = 'none';
            weatherCard.style.display = 'none';
            errorMessage.style.display = 'none';
        }

        // 更新当前日期
        function updateCurrentDate() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };
            currentDate.textContent = now.toLocaleDateString('zh-CN', options);
        }

        // 错误消息处理
        function getErrorMessage(error) {
            switch (error.message) {
                case 'CITY_NOT_FOUND':
                    return '未找到该城市，请检查城市名称是否正确';
                case 'INVALID_API_KEY':
                    return 'API密钥无效，请检查配置';
                case 'API_ERROR':
                    return '天气服务暂时不可用，请稍后重试';
                default:
                    return '网络连接失败，请检查网络连接后重试';
            }
        }

        // 添加重置API密钥的功能（双击标题）
        document.querySelector('header h1').addEventListener('dblclick', () => {
            if (confirm('是否要重新配置API密钥？')) {
                localStorage.removeItem('weatherApiKey');
                API_KEY = '';
                apiKeyInput.value = '';
                apiConfig.style.display = 'block';
            }
        });
    </script>
</body>
</html>
