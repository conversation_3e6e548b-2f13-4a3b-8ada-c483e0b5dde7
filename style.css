* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.search-container {
    margin-bottom: 30px;
}

.search-box {
    display: flex;
    background: white;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.search-box:hover {
    transform: translateY(-2px);
}

#cityInput {
    flex: 1;
    padding: 15px 20px;
    border: none;
    outline: none;
    font-size: 1rem;
    background: transparent;
}

#cityInput::placeholder {
    color: #999;
}

#searchBtn {
    padding: 15px 20px;
    background: #0984e3;
    color: white;
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
    min-width: 60px;
}

#searchBtn:hover {
    background: #0770c4;
}

.weather-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading {
    text-align: center;
    color: white;
    display: none;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.weather-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
    display: none;
    animation: slideUp 0.5s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.weather-header {
    text-align: center;
    margin-bottom: 20px;
}

.weather-header h2 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 5px;
}

.weather-header p {
    color: #666;
    font-size: 0.9rem;
}

.weather-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.temperature {
    font-size: 3rem;
    font-weight: bold;
    color: #0984e3;
}

.unit {
    font-size: 1.5rem;
    color: #666;
}

.weather-icon img {
    width: 80px;
    height: 80px;
}

.weather-description {
    text-align: center;
    margin-bottom: 25px;
}

.weather-description p {
    font-size: 1.2rem;
    color: #555;
    text-transform: capitalize;
}

.weather-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 10px;
    font-size: 0.9rem;
}

.detail-item i {
    color: #0984e3;
    width: 16px;
}

.error-message {
    background: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
    display: none;
    animation: slideUp 0.5s ease;
}

.error-message i {
    font-size: 3rem;
    color: #e74c3c;
    margin-bottom: 15px;
}

.error-message h3 {
    color: #333;
    margin-bottom: 10px;
}

.error-message p {
    color: #666;
    margin-bottom: 20px;
}

#retryBtn {
    background: #0984e3;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: background 0.3s ease;
}

#retryBtn:hover {
    background: #0770c4;
}

footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
    font-size: 0.9rem;
}

footer p {
    margin-bottom: 5px;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .weather-card {
        padding: 20px;
    }
    
    .temperature {
        font-size: 2.5rem;
    }
    
    .weather-details {
        grid-template-columns: 1fr;
    }
}
