// OpenWeatherMap API配置
const API_KEY = 'YOUR_API_KEY_HERE'; // 请替换为您的API密钥
const API_BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';

// DOM元素
const cityInput = document.getElementById('cityInput');
const searchBtn = document.getElementById('searchBtn');
const weatherContainer = document.getElementById('weatherContainer');
const loading = document.getElementById('loading');
const weatherCard = document.getElementById('weatherCard');
const errorMessage = document.getElementById('errorMessage');
const retryBtn = document.getElementById('retryBtn');

// 天气信息元素
const cityName = document.getElementById('cityName');
const currentDate = document.getElementById('currentDate');
const temperature = document.getElementById('temperature');
const weatherIcon = document.getElementById('weatherIcon');
const weatherDescription = document.getElementById('weatherDescription');
const feelsLike = document.getElementById('feelsLike');
const humidity = document.getElementById('humidity');
const windSpeed = document.getElementById('windSpeed');
const pressure = document.getElementById('pressure');
const errorText = document.getElementById('errorText');

// 事件监听器
searchBtn.addEventListener('click', handleSearch);
cityInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        handleSearch();
    }
});
retryBtn.addEventListener('click', handleSearch);

// 页面加载时设置当前日期
document.addEventListener('DOMContentLoaded', () => {
    updateCurrentDate();
});

// 处理搜索
async function handleSearch() {
    const city = cityInput.value.trim();
    
    if (!city) {
        showError('请输入城市名称');
        return;
    }

    if (API_KEY === 'YOUR_API_KEY_HERE') {
        showError('请先在script.js中配置您的OpenWeatherMap API密钥');
        return;
    }

    showLoading();
    
    try {
        const weatherData = await fetchWeatherData(city);
        displayWeatherData(weatherData);
    } catch (error) {
        console.error('获取天气数据失败:', error);
        showError(getErrorMessage(error));
    }
}

// 获取天气数据
async function fetchWeatherData(city) {
    const url = `${API_BASE_URL}?q=${encodeURIComponent(city)}&appid=${API_KEY}&units=metric&lang=zh_cn`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
        if (response.status === 404) {
            throw new Error('CITY_NOT_FOUND');
        } else if (response.status === 401) {
            throw new Error('INVALID_API_KEY');
        } else {
            throw new Error('API_ERROR');
        }
    }
    
    return await response.json();
}

// 显示天气数据
function displayWeatherData(data) {
    // 更新城市名称
    cityName.textContent = `${data.name}, ${data.sys.country}`;
    
    // 更新温度
    temperature.textContent = Math.round(data.main.temp);
    
    // 更新天气图标
    const iconCode = data.weather[0].icon;
    weatherIcon.src = `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
    weatherIcon.alt = data.weather[0].description;
    
    // 更新天气描述
    weatherDescription.textContent = data.weather[0].description;
    
    // 更新详细信息
    feelsLike.textContent = `${Math.round(data.main.feels_like)}°C`;
    humidity.textContent = `${data.main.humidity}%`;
    windSpeed.textContent = `${data.wind.speed} m/s`;
    pressure.textContent = `${data.main.pressure} hPa`;
    
    showWeatherCard();
}

// 显示状态管理
function showLoading() {
    hideAllStates();
    loading.style.display = 'block';
}

function showWeatherCard() {
    hideAllStates();
    weatherCard.style.display = 'block';
}

function showError(message) {
    hideAllStates();
    errorText.textContent = message;
    errorMessage.style.display = 'block';
}

function hideAllStates() {
    loading.style.display = 'none';
    weatherCard.style.display = 'none';
    errorMessage.style.display = 'none';
}

// 更新当前日期
function updateCurrentDate() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    };
    currentDate.textContent = now.toLocaleDateString('zh-CN', options);
}

// 错误消息处理
function getErrorMessage(error) {
    switch (error.message) {
        case 'CITY_NOT_FOUND':
            return '未找到该城市，请检查城市名称是否正确';
        case 'INVALID_API_KEY':
            return 'API密钥无效，请检查配置';
        case 'API_ERROR':
            return '天气服务暂时不可用，请稍后重试';
        default:
            return '网络连接失败，请检查网络连接后重试';
    }
}

// 示例：如果没有API密钥，显示一些示例数据
function showDemoData() {
    const demoData = {
        name: "北京",
        sys: { country: "CN" },
        main: {
            temp: 22,
            feels_like: 25,
            humidity: 65,
            pressure: 1013
        },
        weather: [{
            description: "多云",
            icon: "02d"
        }],
        wind: {
            speed: 3.5
        }
    };
    
    displayWeatherData(demoData);
}
