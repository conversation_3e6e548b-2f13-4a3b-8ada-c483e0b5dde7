# 天气应用 - 单文件版本使用说明

## 📁 文件说明

**weather-app.html** - 这是一个完整的单文件天气应用，包含了所有必要的HTML、CSS和JavaScript代码。

## 🚀 快速开始

### 1. 获取API密钥
1. 访问 [OpenWeatherMap](https://openweathermap.org/api)
2. 点击 "Sign Up" 注册免费账户
3. 登录后在 "API keys" 页面获取您的API密钥

### 2. 使用应用
1. 直接在浏览器中打开 `weather-app.html` 文件
2. 首次使用时会显示API配置界面
3. 输入您的API密钥并点击"保存密钥"
4. 在搜索框中输入城市名称
5. 点击搜索按钮或按回车键查看天气

## ✨ 功能特性

### 🔧 改进功能
- **API密钥管理**: 内置API密钥配置界面，无需编辑代码
- **本地存储**: API密钥自动保存到浏览器本地存储
- **单文件设计**: 所有代码合并在一个HTML文件中，便于分享
- **CDN依赖**: 使用CDN加载Font Awesome图标，无需本地文件

### 🌤️ 天气功能
- 实时天气查询
- 支持全球城市搜索（中英文）
- 显示温度、体感温度、湿度、风速、气压
- 天气图标和描述
- 响应式设计，支持移动设备

### 🛡️ 用户体验
- 智能错误处理和提示
- 加载动画
- 流畅的界面动画
- 现代化UI设计

## 🎯 使用技巧

### 搜索城市
支持多种格式：
- 中文：北京、上海、广州
- 英文：London、New York、Tokyo
- 城市+国家：Beijing,CN

### 重置API密钥
双击页面标题可以重新配置API密钥

### 分享给他人
直接发送 `weather-app.html` 文件即可，接收者只需：
1. 在浏览器中打开文件
2. 配置自己的API密钥
3. 开始使用

## 🔒 隐私说明

- API密钥存储在浏览器本地，不会上传到任何服务器
- 天气数据直接从OpenWeatherMap获取
- 应用不收集任何个人信息

## 🌐 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## ❓ 常见问题

**Q: 为什么需要API密钥？**
A: OpenWeatherMap需要API密钥来验证请求，免费账户每分钟可以请求60次。

**Q: API密钥安全吗？**
A: API密钥存储在您的浏览器本地，不会发送给其他人。但请不要在公共场所使用。

**Q: 搜索不到某个城市怎么办？**
A: 尝试使用英文名称或检查拼写，某些小城市可能不在数据库中。

**Q: 可以离线使用吗？**
A: 不可以，需要网络连接来获取实时天气数据。

## 📞 技术支持

如果遇到问题：
1. 检查网络连接
2. 确认API密钥是否正确
3. 尝试使用不同的城市名称
4. 检查浏览器控制台是否有错误信息

## 📄 许可证

MIT License - 可自由使用、修改和分发。
